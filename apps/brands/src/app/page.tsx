'use client';

import { InputSection } from '@/components/InputSection';
import { BrandingDataSection } from '@/components/BrandingDataSection';
import { VisualPreviewSection } from '@/components/VisualPreviewSection';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Section - Branding Data */}
      <div className="w-full border-b bg-white">
        <BrandingDataSection />
      </div>

      {/* Main Content - Left and Right Sections */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Left Section - Input */}
        <div className="w-1/3 border-r bg-white">
          <InputSection />
        </div>

        {/* Right Section - Visual Preview */}
        <div className="flex-1 bg-gray-50">
          <VisualPreviewSection />
        </div>
      </div>
    </div>
  );
}
