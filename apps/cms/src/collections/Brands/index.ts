import { CollectionConfig } from 'payload';

export const Brands: CollectionConfig = {
  slug: 'brands',
  admin: {
    useAsTitle: 'name', // Shows "name" as the title in the admin panel
    group:'Branding'
  },
  fields: [
    {
      name: 'name',
      label: 'Brand Name',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'visualStyle',
      label: 'Visual Style',
      type: 'text',
      required: false,
      admin: {
        description: 'Brief description of the visual style or keywords',
      },
    },
    {
      name: 'brandAssets',
      type: 'relationship',
      relationTo: 'brandAssets',
      hasMany: true,
    }
  ],
};